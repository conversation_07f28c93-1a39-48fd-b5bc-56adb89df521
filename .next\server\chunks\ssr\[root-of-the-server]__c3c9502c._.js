module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyticsApi": (()=>analyticsApi),
    "apiRequest": (()=>apiRequest),
    "authApi": (()=>authApi),
    "categoriesApi": (()=>categoriesApi),
    "clearTokens": (()=>clearTokens),
    "couponsApi": (()=>couponsApi),
    "getTokens": (()=>getTokens),
    "ordersApi": (()=>ordersApi),
    "paymentsApi": (()=>paymentsApi),
    "providersApi": (()=>providersApi),
    "schedulingApi": (()=>schedulingApi),
    "servicesApi": (()=>servicesApi),
    "setTokens": (()=>setTokens),
    "usersApi": (()=>usersApi)
});
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8000/api") || 'http://localhost:8000/api';
const getTokens = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return {
        access: '',
        refresh: ''
    };
    "TURBOPACK unreachable";
    const access = undefined;
    const refresh = undefined;
};
const setTokens = (tokens)=>{
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
};
const clearTokens = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
};
// Token refresh function
const refreshToken = async ()=>{
    const { refresh } = getTokens();
    if (!refresh) return null;
    try {
        const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                refresh
            })
        });
        if (response.ok) {
            const data = await response.json();
            setTokens({
                access: data.access,
                refresh
            });
            return data.access;
        } else {
            clearTokens();
            return null;
        }
    } catch (error) {
        console.error('Token refresh failed:', error);
        clearTokens();
        return null;
    }
};
const apiRequest = async (endpoint, options = {})=>{
    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
    const { access } = getTokens();
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    if (access) {
        headers.Authorization = `Bearer ${access}`;
    }
    console.log(`API Request: ${options.method || 'GET'} ${url}`);
    if (options.body) {
        console.log('Request body:', options.body);
    }
    let response = await fetch(url, {
        ...options,
        headers
    });
    // If token expired, try to refresh and retry
    if (response.status === 401 && access) {
        console.log('Token expired, attempting refresh...');
        const newToken = await refreshToken();
        if (newToken) {
            headers.Authorization = `Bearer ${newToken}`;
            response = await fetch(url, {
                ...options,
                headers
            });
        } else {
            // Redirect to login if refresh fails
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            throw new Error('Authentication failed');
        }
    }
    const data = await response.json();
    console.log(`API Response: ${response.status}`, data);
    if (!response.ok) {
        const error = {
            message: data.message || data.error || 'An error occurred',
            details: data.details || data,
            status: response.status
        };
        throw error;
    }
    return data;
};
const authApi = {
    // Staff login with email/password
    loginStaff: (credentials)=>apiRequest('/auth/login/email/', {
            method: 'POST',
            body: JSON.stringify(credentials)
        }),
    // Get user profile
    getProfile: ()=>apiRequest('/auth/profile/'),
    // Update user profile
    updateProfile: (data)=>apiRequest('/auth/profile/', {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Change password
    changePassword: (data)=>apiRequest('/auth/change-password/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Logout
    logout: ()=>apiRequest('/auth/logout/', {
            method: 'POST'
        })
};
const ordersApi = {
    // Get all orders (staff can see all)
    getOrders: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/orders/${query}`);
    },
    // Get order details
    getOrderDetail: (orderNumber)=>apiRequest(`/orders/${orderNumber}/`),
    // Update order status
    updateOrderStatus: (orderNumber, data)=>apiRequest(`/orders/${orderNumber}/status/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Assign provider to order
    assignProvider: (orderNumber, data)=>apiRequest(`/orders/${orderNumber}/assign-provider/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Cancel order
    cancelOrder: (orderNumber, data)=>apiRequest(`/orders/${orderNumber}/cancel/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const usersApi = {
    // Get all users
    getUsers: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/auth/users/${query}`);
    },
    // Get user details
    getUserDetail: (userId)=>apiRequest(`/auth/users/${userId}/`),
    // Update user
    updateUser: (userId, data)=>apiRequest(`/auth/users/${userId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Lock/unlock user
    toggleUserLock: (userId, data)=>apiRequest(`/auth/users/${userId}/toggle-lock/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const providersApi = {
    // Get all providers
    getProviders: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/providers/${query}`);
    },
    // Get provider details
    getProviderDetail: (providerId)=>apiRequest(`/providers/${providerId}/`),
    // Update provider verification status
    updateProviderVerification: (providerId, data)=>apiRequest(`/providers/${providerId}/verification/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Toggle provider availability
    toggleProviderAvailability: (providerId, data)=>apiRequest(`/providers/${providerId}/availability/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const servicesApi = {
    // Get all services
    getServices: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/catalogue/services/${query}`);
    },
    // Get service details
    getServiceDetail: (serviceId)=>apiRequest(`/catalogue/services/${serviceId}/`),
    // Create service
    createService: (data)=>apiRequest('/catalogue/services/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update service
    updateService: (serviceId, data)=>apiRequest(`/catalogue/services/${serviceId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Delete service
    deleteService: (serviceId)=>apiRequest(`/catalogue/services/${serviceId}/`, {
            method: 'DELETE'
        })
};
const categoriesApi = {
    // Get all categories
    getCategories: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/catalogue/categories/${query}`);
    },
    // Get category details
    getCategoryDetail: (categoryId)=>apiRequest(`/catalogue/categories/${categoryId}/`),
    // Create category
    createCategory: (data)=>apiRequest('/catalogue/categories/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update category
    updateCategory: (categoryId, data)=>apiRequest(`/catalogue/categories/${categoryId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Delete category
    deleteCategory: (categoryId)=>apiRequest(`/catalogue/categories/${categoryId}/`, {
            method: 'DELETE'
        })
};
const paymentsApi = {
    // Get all transactions
    getTransactions: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/payments/transactions/${query}`);
    },
    // Get transaction details
    getTransactionDetail: (transactionId)=>apiRequest(`/payments/transactions/${transactionId}/`),
    // Process refund
    processRefund: (transactionId, data)=>apiRequest(`/payments/transactions/${transactionId}/refund/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const analyticsApi = {
    // Get dashboard stats
    getDashboardStats: ()=>apiRequest('/analytics/dashboard/'),
    // Get order statistics
    getOrderStats: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/analytics/orders/${query}`);
    },
    // Get revenue statistics
    getRevenueStats: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/analytics/revenue/${query}`);
    },
    // Get user statistics
    getUserStats: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/analytics/users/${query}`);
    }
};
const schedulingApi = {
    // Get time slots
    getTimeSlots: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/scheduling/slots/${query}`);
    },
    // Create time slot
    createTimeSlot: (data)=>apiRequest('/scheduling/slots/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update time slot
    updateTimeSlot: (slotId, data)=>apiRequest(`/scheduling/slots/${slotId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Block time slot
    blockTimeSlot: (slotId, data)=>apiRequest(`/scheduling/slots/${slotId}/block/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Get slot bookings
    getSlotBookings: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/scheduling/bookings/${query}`);
    }
};
const couponsApi = {
    // Get all coupons
    getCoupons: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/coupons/${query}`);
    },
    // Get coupon details
    getCouponDetail: (couponId)=>apiRequest(`/coupons/${couponId}/`),
    // Create coupon
    createCoupon: (data)=>apiRequest('/coupons/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update coupon
    updateCoupon: (couponId, data)=>apiRequest(`/coupons/${couponId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Delete coupon
    deleteCoupon: (couponId)=>apiRequest(`/coupons/${couponId}/`, {
            method: 'DELETE'
        }),
    // Toggle coupon status
    toggleCouponStatus: (couponId, data)=>apiRequest(`/coupons/${couponId}/toggle/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
}}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const isAuthenticated = !!user;
    // Initialize auth state on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeAuth = async ()=>{
            const { refresh } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokens"])();
            if (refresh) {
                try {
                    // Try to get user profile to verify token validity
                    const userProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authApi"].getProfile();
                    // Ensure user is staff
                    if (userProfile.user_type !== 'STAFF') {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clearTokens"])();
                        throw new Error('Access denied: Staff access required');
                    }
                    setUser(userProfile);
                } catch (error) {
                    // Token is invalid, clear it
                    console.error('Auth initialization failed:', error);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clearTokens"])();
                }
            }
            setIsLoading(false);
        };
        initializeAuth();
    }, []);
    const login = async (credentials)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authApi"].loginStaff(credentials);
            // Ensure user is staff
            if (response.user.user_type !== 'STAFF') {
                throw new Error('Access denied: Staff access required');
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setTokens"])(response.tokens);
            setUser(response.user);
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    };
    const logout = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authApi"].logout();
        } catch (error) {
            // Even if logout fails on server, clear local tokens
            console.error('Logout error:', error);
        } finally{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clearTokens"])();
            setUser(null);
        }
    };
    const updateUser = (userData)=>{
        if (user) {
            setUser({
                ...user,
                ...userData
            });
        }
    };
    const refreshUserProfile = async ()=>{
        if (isAuthenticated) {
            try {
                const userProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authApi"].getProfile();
                setUser(userProfile);
            } catch (error) {
                console.error('Failed to refresh user profile:', error);
            }
        }
    };
    const value = {
        user,
        isAuthenticated,
        isLoading,
        login,
        logout,
        updateUser,
        refreshUserProfile
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 124,
        columnNumber: 5
    }, this);
};
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c3c9502c._.js.map