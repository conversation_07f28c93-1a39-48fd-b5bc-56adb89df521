'use client';

import React, { useEffect, useState } from 'react';
import { 
  ShoppingBag, 
  DollarSign,
  Clock,
  CheckCircle,
  Star,
  TrendingUp,
  AlertCircle,
  Calendar
} from 'lucide-react';
import { MobileLayout } from '@/components/layout/MobileLayout';
import { Card, CardHeader, CardTitle, CardContent, StatsCard } from '@/components/ui/Card';
import { LoadingCard } from '@/components/ui/LoadingSpinner';
import { Badge, StatusBadge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { providerApi, ordersApi } from '@/lib/api';
import { formatCurrency, formatRelativeTime, formatRating } from '@/lib/utils';
import { ProviderStats, Order } from '@/types/api';

export default function DashboardPage() {
  const [stats, setStats] = useState<ProviderStats | null>(null);
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Fetch provider stats and recent orders in parallel
        const [statsResponse, ordersResponse] = await Promise.all([
          providerApi.getStats(),
          ordersApi.getOrders({ limit: 5, ordering: '-created_at' })
        ]);

        setStats(statsResponse as ProviderStats);
        setRecentOrders(ordersResponse.results || []);
      } catch (err: any) {
        setError(err.message || 'Failed to load dashboard data');
        console.error('Dashboard data fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <MobileLayout>
        <div className="space-y-6">
          {/* Stats Cards Loading */}
          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
          
          {/* Recent Orders Loading */}
          <LoadingCard />
        </div>
      </MobileLayout>
    );
  }

  if (error) {
    return (
      <MobileLayout>
        <div className="text-center py-12">
          <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading dashboard</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <h2 className="text-xl font-bold mb-2">Welcome back!</h2>
          <p className="text-blue-100">Here's your business overview for today</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 gap-4">
          <StatsCard
            title="Total Orders"
            value={stats?.total_orders || 0}
            icon={<ShoppingBag className="w-6 h-6" />}
          />
          
          <StatsCard
            title="Total Earnings"
            value={formatCurrency(stats?.total_earnings || '0')}
            icon={<DollarSign className="w-6 h-6" />}
          />
          
          <StatsCard
            title="Pending Orders"
            value={stats?.pending_orders || 0}
            icon={<Clock className="w-6 h-6" />}
          />
          
          <StatsCard
            title="Completed"
            value={stats?.completed_orders || 0}
            icon={<CheckCircle className="w-6 h-6" />}
          />
        </div>

        {/* Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Performance Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Star className="w-5 h-5 text-yellow-500 mr-1" />
                  <span className="text-2xl font-bold text-gray-900">
                    {formatRating(stats?.average_rating || '0')}
                  </span>
                </div>
                <p className="text-sm text-gray-500">Average Rating</p>
                <p className="text-xs text-gray-400">{stats?.total_reviews || 0} reviews</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatCurrency(stats?.this_month_earnings || '0')}
                </div>
                <p className="text-sm text-gray-500">This Month</p>
                <p className="text-xs text-gray-400">Earnings</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" className="h-auto py-4 flex-col">
                <Calendar className="w-6 h-6 mb-2" />
                <span className="text-sm">View Schedule</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex-col">
                <DollarSign className="w-6 h-6 mb-2" />
                <span className="text-sm">Earnings</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
          </CardHeader>
          <CardContent>
            {recentOrders.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingBag className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-2 text-sm text-gray-500">No recent orders</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">#{order.order_number}</span>
                      <StatusBadge status={order.status} />
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-2">
                      <p>{order.customer_name}</p>
                      <p>{order.items_count} item{order.items_count !== 1 ? 's' : ''}</p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900">
                        {formatCurrency(order.total_amount)}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatRelativeTime(order.created_at)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Orders in Progress */}
        {stats && stats.in_progress_orders > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="w-5 h-5 mr-2 text-blue-500" />
                Orders in Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-4">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {stats.in_progress_orders}
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  {stats.in_progress_orders === 1 ? 'Order' : 'Orders'} currently in progress
                </p>
                <Button size="sm" className="w-full">
                  View Active Orders
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MobileLayout>
  );
}
