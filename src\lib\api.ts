import { AuthTokens, LoginResponse, User, ApiError, OTPResponse } from '@/types/api';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

// Token management
export const getTokens = (): AuthTokens => {
  if (typeof window === 'undefined') return { access: '', refresh: '' };
  
  const access = localStorage.getItem('access_token') || '';
  const refresh = localStorage.getItem('refresh_token') || '';
  return { access, refresh };
};

export const setTokens = (tokens: AuthTokens): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem('access_token', tokens.access);
  localStorage.setItem('refresh_token', tokens.refresh);
};

export const clearTokens = (): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
};

// Token refresh function
const refreshToken = async (): Promise<string | null> => {
  const { refresh } = getTokens();
  if (!refresh) return null;

  try {
    const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh }),
    });

    if (response.ok) {
      const data = await response.json();
      setTokens({ access: data.access, refresh });
      return data.access;
    } else {
      clearTokens();
      return null;
    }
  } catch (error) {
    console.error('Token refresh failed:', error);
    clearTokens();
    return null;
  }
};

// API request wrapper with automatic token refresh
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;

  const { access } = getTokens();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  if (access) {
    headers.Authorization = `Bearer ${access}`;
  }

  console.log(`API Request: ${options.method || 'GET'} ${url}`);
  if (options.body) {
    console.log('Request body:', options.body);
  }

  let response = await fetch(url, {
    ...options,
    headers,
  });

  // If token expired, try to refresh and retry
  if (response.status === 401 && access) {
    console.log('Token expired, attempting refresh...');
    const newToken = await refreshToken();
    
    if (newToken) {
      headers.Authorization = `Bearer ${newToken}`;
      response = await fetch(url, {
        ...options,
        headers,
      });
    } else {
      // Redirect to login if refresh fails
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login';
      }
      throw new Error('Authentication failed');
    }
  }

  const data = await response.json();
  console.log(`API Response: ${response.status}`, data);

  if (!response.ok) {
    const error: ApiError = {
      message: data.message || data.error || 'An error occurred',
      details: data.details || data,
      status: response.status,
    };
    throw error;
  }

  return data;
};

// Authentication API
export const authApi = {
  // Send OTP for mobile login
  sendOTP: (mobile_number: string) =>
    apiRequest<OTPResponse>('/auth/otp/send/', {
      method: 'POST',
      body: JSON.stringify({ mobile_number }),
    }),

  // Verify OTP and login
  loginWithOTP: (credentials: { mobile_number: string; otp: string }) =>
    apiRequest<LoginResponse>('/auth/login/mobile/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    }),

  // Get user profile
  getProfile: () =>
    apiRequest<User>('/auth/profile/'),

  // Update user profile
  updateProfile: (data: Partial<User>) =>
    apiRequest<User>('/auth/profile/', {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Logout
  logout: () =>
    apiRequest('/auth/logout/', {
      method: 'POST',
    }),
};

// Provider API
export const providerApi = {
  // Get provider profile
  getProfile: () =>
    apiRequest('/providers/profile/'),

  // Update provider profile
  updateProfile: (data: any) =>
    apiRequest('/providers/profile/', {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Get provider stats
  getStats: () =>
    apiRequest('/providers/stats/'),

  // Update availability status
  updateAvailability: (is_available: boolean) =>
    apiRequest('/providers/availability/', {
      method: 'POST',
      body: JSON.stringify({ is_available }),
    }),

  // Get availability schedule
  getAvailabilitySchedule: () =>
    apiRequest('/providers/schedule/'),

  // Update availability schedule
  updateAvailabilitySchedule: (schedule: any[]) =>
    apiRequest('/providers/schedule/', {
      method: 'POST',
      body: JSON.stringify({ schedule }),
    }),
};

// Orders API
export const ordersApi = {
  // Get assigned orders
  getOrders: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/orders/${query}`);
  },

  // Get order details
  getOrderDetail: (orderNumber: string) =>
    apiRequest(`/orders/${orderNumber}/`),

  // Update order status
  updateOrderStatus: (orderNumber: string, data: { status: string; notes?: string }) =>
    apiRequest(`/orders/${orderNumber}/status/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Accept order
  acceptOrder: (orderNumber: string, data?: { notes?: string }) =>
    apiRequest(`/orders/${orderNumber}/accept/`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    }),

  // Start order
  startOrder: (orderNumber: string, data?: { notes?: string }) =>
    apiRequest(`/orders/${orderNumber}/start/`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    }),

  // Complete order
  completeOrder: (orderNumber: string, data?: { notes?: string; completion_photos?: string[] }) =>
    apiRequest(`/orders/${orderNumber}/complete/`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    }),
};

// Payments API
export const paymentsApi = {
  // Get earnings
  getEarnings: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/providers/earnings/${query}`);
  },

  // Get earnings breakdown
  getEarningsBreakdown: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/providers/earnings/breakdown/${query}`);
  },

  // Get payment transactions
  getTransactions: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/payments/transactions/${query}`);
  },
};

// Scheduling API
export const schedulingApi = {
  // Get assigned time slots
  getTimeSlots: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/scheduling/slots/${query}`);
  },

  // Get slot bookings
  getSlotBookings: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/scheduling/bookings/${query}`);
  },

  // Update booking status
  updateBookingStatus: (bookingId: number, data: { status: string; notes?: string }) =>
    apiRequest(`/scheduling/bookings/${bookingId}/status/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Reviews API
export const reviewsApi = {
  // Get provider reviews
  getReviews: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/providers/reviews/${query}`);
  },

  // Respond to review
  respondToReview: (reviewId: number, data: { response: string }) =>
    apiRequest(`/providers/reviews/${reviewId}/respond/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Notifications API
export const notificationsApi = {
  // Get notifications
  getNotifications: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/notifications/${query}`);
  },

  // Mark notification as read
  markAsRead: (notificationId: number) =>
    apiRequest(`/notifications/${notificationId}/read/`, {
      method: 'POST',
    }),

  // Mark all notifications as read
  markAllAsRead: () =>
    apiRequest('/notifications/mark-all-read/', {
      method: 'POST',
    }),
};
