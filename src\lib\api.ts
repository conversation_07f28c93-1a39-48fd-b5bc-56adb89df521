import { AuthTokens, LoginResponse, User, ApiError } from '@/types/api';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

// Token management
export const getTokens = (): AuthTokens => {
  if (typeof window === 'undefined') return { access: '', refresh: '' };
  
  const access = localStorage.getItem('access_token') || '';
  const refresh = localStorage.getItem('refresh_token') || '';
  return { access, refresh };
};

export const setTokens = (tokens: AuthTokens): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem('access_token', tokens.access);
  localStorage.setItem('refresh_token', tokens.refresh);
};

export const clearTokens = (): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
};

// Token refresh function
const refreshToken = async (): Promise<string | null> => {
  const { refresh } = getTokens();
  if (!refresh) return null;

  try {
    const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh }),
    });

    if (response.ok) {
      const data = await response.json();
      setTokens({ access: data.access, refresh });
      return data.access;
    } else {
      clearTokens();
      return null;
    }
  } catch (error) {
    console.error('Token refresh failed:', error);
    clearTokens();
    return null;
  }
};

// API request wrapper with automatic token refresh
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;

  const { access } = getTokens();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  if (access) {
    headers.Authorization = `Bearer ${access}`;
  }

  console.log(`API Request: ${options.method || 'GET'} ${url}`);
  if (options.body) {
    console.log('Request body:', options.body);
  }

  let response = await fetch(url, {
    ...options,
    headers,
  });

  // If token expired, try to refresh and retry
  if (response.status === 401 && access) {
    console.log('Token expired, attempting refresh...');
    const newToken = await refreshToken();
    
    if (newToken) {
      headers.Authorization = `Bearer ${newToken}`;
      response = await fetch(url, {
        ...options,
        headers,
      });
    } else {
      // Redirect to login if refresh fails
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login';
      }
      throw new Error('Authentication failed');
    }
  }

  const data = await response.json();
  console.log(`API Response: ${response.status}`, data);

  if (!response.ok) {
    const error: ApiError = {
      message: data.message || data.error || 'An error occurred',
      details: data.details || data,
      status: response.status,
    };
    throw error;
  }

  return data;
};

// Authentication API
export const authApi = {
  // Staff login with email/password
  loginStaff: (credentials: { email: string; password: string }) =>
    apiRequest<LoginResponse>('/auth/login/email/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    }),

  // Get user profile
  getProfile: () =>
    apiRequest<User>('/auth/profile/'),

  // Update user profile
  updateProfile: (data: Partial<User>) =>
    apiRequest<User>('/auth/profile/', {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Change password
  changePassword: (data: { old_password: string; new_password: string; confirm_password: string }) =>
    apiRequest('/auth/change-password/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Logout
  logout: () =>
    apiRequest('/auth/logout/', {
      method: 'POST',
    }),
};

// Orders API
export const ordersApi = {
  // Get all orders (staff can see all)
  getOrders: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/orders/${query}`);
  },

  // Get order details
  getOrderDetail: (orderNumber: string) =>
    apiRequest(`/orders/${orderNumber}/`),

  // Update order status
  updateOrderStatus: (orderNumber: string, data: { status: string; admin_notes?: string }) =>
    apiRequest(`/orders/${orderNumber}/status/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Assign provider to order
  assignProvider: (orderNumber: string, data: { provider_id: number; admin_notes?: string }) =>
    apiRequest(`/orders/${orderNumber}/assign-provider/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Cancel order
  cancelOrder: (orderNumber: string, data: { reason: string; admin_notes?: string }) =>
    apiRequest(`/orders/${orderNumber}/cancel/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Users API
export const usersApi = {
  // Get all users
  getUsers: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/auth/users/${query}`);
  },

  // Get user details
  getUserDetail: (userId: number) =>
    apiRequest(`/auth/users/${userId}/`),

  // Update user
  updateUser: (userId: number, data: Partial<User>) =>
    apiRequest(`/auth/users/${userId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Lock/unlock user
  toggleUserLock: (userId: number, data: { is_locked: boolean; lockout_duration?: number }) =>
    apiRequest(`/auth/users/${userId}/toggle-lock/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Providers API
export const providersApi = {
  // Get all providers
  getProviders: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/providers/${query}`);
  },

  // Get provider details
  getProviderDetail: (providerId: number) =>
    apiRequest(`/providers/${providerId}/`),

  // Update provider verification status
  updateProviderVerification: (providerId: number, data: { verification_status: string; admin_notes?: string }) =>
    apiRequest(`/providers/${providerId}/verification/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Toggle provider availability
  toggleProviderAvailability: (providerId: number, data: { is_available: boolean }) =>
    apiRequest(`/providers/${providerId}/availability/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Services API
export const servicesApi = {
  // Get all services
  getServices: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/catalogue/services/${query}`);
  },

  // Get service details
  getServiceDetail: (serviceId: number) =>
    apiRequest(`/catalogue/services/${serviceId}/`),

  // Create service
  createService: (data: any) =>
    apiRequest('/catalogue/services/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update service
  updateService: (serviceId: number, data: any) =>
    apiRequest(`/catalogue/services/${serviceId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete service
  deleteService: (serviceId: number) =>
    apiRequest(`/catalogue/services/${serviceId}/`, {
      method: 'DELETE',
    }),
};

// Categories API
export const categoriesApi = {
  // Get all categories
  getCategories: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/catalogue/categories/${query}`);
  },

  // Get category details
  getCategoryDetail: (categoryId: number) =>
    apiRequest(`/catalogue/categories/${categoryId}/`),

  // Create category
  createCategory: (data: any) =>
    apiRequest('/catalogue/categories/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update category
  updateCategory: (categoryId: number, data: any) =>
    apiRequest(`/catalogue/categories/${categoryId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete category
  deleteCategory: (categoryId: number) =>
    apiRequest(`/catalogue/categories/${categoryId}/`, {
      method: 'DELETE',
    }),
};

// Payments API
export const paymentsApi = {
  // Get all transactions
  getTransactions: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/payments/transactions/${query}`);
  },

  // Get transaction details
  getTransactionDetail: (transactionId: string) =>
    apiRequest(`/payments/transactions/${transactionId}/`),

  // Process refund
  processRefund: (transactionId: string, data: { amount?: string; reason: string }) =>
    apiRequest(`/payments/transactions/${transactionId}/refund/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Analytics API
export const analyticsApi = {
  // Get dashboard stats
  getDashboardStats: () =>
    apiRequest('/analytics/dashboard/'),

  // Get order statistics
  getOrderStats: (params?: { start_date?: string; end_date?: string; period?: string }) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/analytics/orders/${query}`);
  },

  // Get revenue statistics
  getRevenueStats: (params?: { start_date?: string; end_date?: string; period?: string }) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/analytics/revenue/${query}`);
  },

  // Get user statistics
  getUserStats: (params?: { start_date?: string; end_date?: string }) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/analytics/users/${query}`);
  },
};

// Scheduling API
export const schedulingApi = {
  // Get time slots
  getTimeSlots: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/scheduling/slots/${query}`);
  },

  // Create time slot
  createTimeSlot: (data: any) =>
    apiRequest('/scheduling/slots/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update time slot
  updateTimeSlot: (slotId: number, data: any) =>
    apiRequest(`/scheduling/slots/${slotId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Block time slot
  blockTimeSlot: (slotId: number, data: { reason: string; blocked_by: string }) =>
    apiRequest(`/scheduling/slots/${slotId}/block/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Get slot bookings
  getSlotBookings: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/scheduling/bookings/${query}`);
  },
};

// Coupons API
export const couponsApi = {
  // Get all coupons
  getCoupons: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/coupons/${query}`);
  },

  // Get coupon details
  getCouponDetail: (couponId: number) =>
    apiRequest(`/coupons/${couponId}/`),

  // Create coupon
  createCoupon: (data: any) =>
    apiRequest('/coupons/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update coupon
  updateCoupon: (couponId: number, data: any) =>
    apiRequest(`/coupons/${couponId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete coupon
  deleteCoupon: (couponId: number) =>
    apiRequest(`/coupons/${couponId}/`, {
      method: 'DELETE',
    }),

  // Toggle coupon status
  toggleCouponStatus: (couponId: number, data: { is_active: boolean }) =>
    apiRequest(`/coupons/${couponId}/toggle/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};
