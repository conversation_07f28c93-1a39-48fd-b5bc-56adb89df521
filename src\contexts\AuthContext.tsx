'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, AuthTokens, LoginResponse } from '@/types/api';
import { authApi, setTokens, getTokens, clearTokens } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { email: string; password: string }) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  refreshUserProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      const { refresh } = getTokens();
      
      if (refresh) {
        try {
          // Try to get user profile to verify token validity
          const userProfile = await authApi.getProfile();
          
          // Ensure user is staff
          if (userProfile.user_type !== 'STAFF') {
            clearTokens();
            throw new Error('Access denied: Staff access required');
          }
          
          setUser(userProfile as User);
        } catch (error) {
          // Token is invalid, clear it
          console.error('Auth initialization failed:', error);
          clearTokens();
        }
      }
      
      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (credentials: { email: string; password: string }) => {
    try {
      const response = await authApi.loginStaff(credentials);
      
      // Ensure user is staff
      if (response.user.user_type !== 'STAFF') {
        throw new Error('Access denied: Staff access required');
      }
      
      setTokens(response.tokens);
      setUser(response.user);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authApi.logout();
    } catch (error) {
      // Even if logout fails on server, clear local tokens
      console.error('Logout error:', error);
    } finally {
      clearTokens();
      setUser(null);
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  const refreshUserProfile = async () => {
    if (isAuthenticated) {
      try {
        const userProfile = await authApi.getProfile();
        setUser(userProfile as User);
      } catch (error) {
        console.error('Failed to refresh user profile:', error);
      }
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateUser,
    refreshUserProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
