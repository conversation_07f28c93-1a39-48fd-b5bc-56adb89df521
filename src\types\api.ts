// Authentication Types
export interface User {
  id: number;
  email?: string;
  mobile_number?: string;
  name: string;
  user_type: 'CUSTOMER' | 'PROVIDER' | 'STAFF';
  is_verified: boolean;
  profile_picture?: string;
  date_joined: string;
  is_active: boolean;
  is_staff: boolean;
  is_locked: boolean;
  lockout_until?: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface LoginResponse {
  message: string;
  user: User;
  tokens: AuthTokens;
}

// Address Types
export interface Address {
  id: number;
  address_type: 'HOME' | 'WORK' | 'OTHER';
  street: string;
  city: string;
  state: string;
  zip_code: string;
  landmark?: string;
  is_default: boolean;
  created_at: string;
}

// Category Types
export interface Category {
  id: number;
  name: string;
  slug: string;
  image?: string;
  description: string;
  parent?: number;
  parent_name?: string;
  level: number;
  services_count: number;
  is_active: boolean;
  children?: Category[];
  services?: Service[];
  created_at?: string;
  updated_at?: string;
}

// Service Types
export interface Service {
  id: number;
  title: string;
  slug: string;
  image?: string;
  description: string;
  base_price: string;
  discount_price?: string;
  current_price: string;
  discount_percentage?: number;
  time_to_complete: string;
  category: number;
  category_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  requires_partial_payment?: boolean;
  partial_payment_type?: 'percentage' | 'fixed';
  partial_payment_value?: string;
  partial_payment_description?: string;
}

// Order Types
export interface OrderItem {
  id: number;
  service_id: number;
  service_title: string;
  quantity: number;
  unit_price: string;
  discount_per_unit: string;
  total_price: string;
  estimated_duration: string;
  special_instructions?: string;
}

export interface Order {
  id: number;
  order_number: string;
  customer: number;
  customer_name: string;
  customer_mobile: string;
  status: 'pending' | 'confirmed' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'refunded';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: 'razorpay' | 'cod';
  subtotal: string;
  tax_amount: string;
  discount_amount: string;
  minimum_order_fee: string;
  total_amount: string;
  coupon_code?: string;
  coupon_discount: string;
  delivery_address: any;
  assigned_provider?: number;
  assigned_provider_name?: string;
  assigned_provider_mobile?: string;
  scheduled_date: string;
  scheduled_time_slot: string;
  payment_id?: string;
  customer_notes?: string;
  admin_notes?: string;
  cgst_amount: string;
  sgst_amount: string;
  igst_amount: string;
  ugst_amount: string;
  service_charge: string;
  items: OrderItem[];
  items_count: number;
  can_cancel: boolean;
  can_reschedule: boolean;
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  completed_at?: string;
  cancelled_at?: string;
}

// Provider Types
export interface Provider {
  id: number;
  user: number;
  user_name: string;
  user_mobile: string;
  user_email?: string;
  business_name?: string;
  business_description?: string;
  business_address?: string;
  verification_status: 'pending' | 'verified' | 'rejected';
  verification_documents?: any;
  services_offered: number[];
  service_areas: string[];
  is_available: boolean;
  accepts_new_orders: boolean;
  rating: string;
  total_reviews: number;
  total_orders_completed: number;
  created_at: string;
  updated_at: string;
}

// Payment Types
export interface PaymentTransaction {
  id: number;
  transaction_id: string;
  user: number;
  order_id: string;
  order_number: string;
  payment_method: string;
  amount: string;
  currency: string;
  status: 'initiated' | 'pending' | 'success' | 'failed' | 'refunded';
  gateway_transaction_id?: string;
  gateway_response?: any;
  created_at: string;
  updated_at: string;
}

// Analytics Types
export interface DashboardStats {
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  total_revenue: string;
  total_customers: number;
  total_providers: number;
  orders_today: number;
  revenue_today: string;
}

export interface OrderStats {
  date: string;
  orders: number;
  revenue: string;
}

// Paginated Response Types
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  details?: Record<string, string[]>;
  status?: number;
}

// Coupon Types
export interface Coupon {
  id: number;
  code: string;
  name: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: string;
  minimum_order_amount?: string;
  maximum_discount_amount?: string;
  usage_limit?: number;
  usage_limit_per_user?: number;
  valid_from: string;
  valid_until: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Scheduling Types
export interface TimeSlot {
  id: number;
  date: string;
  start_time: string;
  end_time: string;
  status: 'available' | 'booked' | 'blocked';
  current_bookings: number;
  max_bookings: number;
  booked_orders: string[];
  blocked_reason?: string;
  blocked_by?: string;
  notes?: string;
}

export interface SlotBooking {
  id: number;
  time_slot: number;
  order_id: string;
  customer_mobile: string;
  customer_name: string;
  booking_status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  quantity: number;
  service_names: string[];
  booked_at: string;
  confirmed_at?: string;
  cancelled_at?: string;
  completed_at?: string;
  customer_notes?: string;
  admin_notes?: string;
}
