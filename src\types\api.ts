// Authentication Types
export interface User {
  id: number;
  email?: string;
  mobile_number?: string;
  name: string;
  user_type: 'CUSTOMER' | 'PROVIDER' | 'STAFF';
  is_verified: boolean;
  profile_picture?: string;
  date_joined: string;
  is_active: boolean;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface LoginResponse {
  message: string;
  user: User;
  tokens: AuthTokens;
}

export interface OTPResponse {
  success: boolean;
  message: string;
  mobile_number: string;
}

// Provider Profile Types
export interface ProviderProfile {
  id: number;
  user: number;
  user_name: string;
  user_mobile: string;
  user_email?: string;
  business_name?: string;
  business_description?: string;
  business_address?: string;
  verification_status: 'pending' | 'verified' | 'rejected';
  verification_documents?: any;
  services_offered: number[];
  service_areas: string[];
  is_available: boolean;
  accepts_new_orders: boolean;
  rating: string;
  total_reviews: number;
  total_orders_completed: number;
  created_at: string;
  updated_at: string;
}

// Order Types
export interface OrderItem {
  id: number;
  service_id: number;
  service_title: string;
  quantity: number;
  unit_price: string;
  discount_per_unit: string;
  total_price: string;
  estimated_duration: string;
  special_instructions?: string;
}

export interface Order {
  id: number;
  order_number: string;
  customer: number;
  customer_name: string;
  customer_mobile: string;
  status: 'pending' | 'confirmed' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'refunded';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: 'razorpay' | 'cod';
  subtotal: string;
  tax_amount: string;
  discount_amount: string;
  minimum_order_fee: string;
  total_amount: string;
  coupon_code?: string;
  coupon_discount: string;
  delivery_address: any;
  assigned_provider?: number;
  assigned_provider_name?: string;
  assigned_provider_mobile?: string;
  scheduled_date: string;
  scheduled_time_slot: string;
  payment_id?: string;
  customer_notes?: string;
  admin_notes?: string;
  cgst_amount: string;
  sgst_amount: string;
  igst_amount: string;
  ugst_amount: string;
  service_charge: string;
  items: OrderItem[];
  items_count: number;
  can_cancel: boolean;
  can_reschedule: boolean;
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  completed_at?: string;
  cancelled_at?: string;
}

// Service Types
export interface Service {
  id: number;
  title: string;
  slug: string;
  image?: string;
  description: string;
  base_price: string;
  discount_price?: string;
  current_price: string;
  discount_percentage?: number;
  time_to_complete: string;
  category: number;
  category_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Payment Types
export interface PaymentTransaction {
  id: number;
  transaction_id: string;
  user: number;
  order_id: string;
  order_number: string;
  payment_method: string;
  amount: string;
  currency: string;
  status: 'initiated' | 'pending' | 'success' | 'failed' | 'refunded';
  gateway_transaction_id?: string;
  gateway_response?: any;
  created_at: string;
  updated_at: string;
}

// Dashboard Stats for Provider
export interface ProviderStats {
  total_orders: number;
  pending_orders: number;
  in_progress_orders: number;
  completed_orders: number;
  total_earnings: string;
  this_month_earnings: string;
  average_rating: string;
  total_reviews: number;
}

// Scheduling Types
export interface TimeSlot {
  id: number;
  date: string;
  start_time: string;
  end_time: string;
  status: 'available' | 'booked' | 'blocked';
  current_bookings: number;
  max_bookings: number;
  booked_orders: string[];
  blocked_reason?: string;
  blocked_by?: string;
  notes?: string;
}

export interface SlotBooking {
  id: number;
  time_slot: number;
  order_id: string;
  customer_mobile: string;
  customer_name: string;
  booking_status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  quantity: number;
  service_names: string[];
  booked_at: string;
  confirmed_at?: string;
  cancelled_at?: string;
  completed_at?: string;
  customer_notes?: string;
  admin_notes?: string;
}

// Paginated Response Types
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  details?: Record<string, string[]>;
  status?: number;
}

// Notification Types
export interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  is_read: boolean;
  created_at: string;
  order_id?: string;
  order_number?: string;
}

// Availability Types
export interface ProviderAvailability {
  id: number;
  provider: number;
  day_of_week: number; // 0 = Monday, 6 = Sunday
  start_time: string;
  end_time: string;
  is_available: boolean;
  break_start_time?: string;
  break_end_time?: string;
  created_at: string;
  updated_at: string;
}

// Review Types
export interface Review {
  id: number;
  order: number;
  order_number: string;
  customer_name: string;
  rating: number;
  comment?: string;
  created_at: string;
}

// Earnings Types
export interface EarningsBreakdown {
  order_id: string;
  order_number: string;
  customer_name: string;
  service_amount: string;
  commission_rate: string;
  commission_amount: string;
  net_earnings: string;
  payment_status: string;
  completed_at: string;
}

export interface EarningsStats {
  total_earnings: string;
  pending_earnings: string;
  paid_earnings: string;
  commission_total: string;
  orders_count: number;
  average_order_value: string;
}
