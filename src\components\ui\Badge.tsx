import React from 'react';
import { cn } from '@/lib/utils';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className,
}) => {
  const baseClasses = 'inline-flex items-center font-medium rounded-full';
  
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800',
  };
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-2.5 py-0.5 text-sm',
    lg: 'px-3 py-1 text-base',
  };

  return (
    <span
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
    >
      {children}
    </span>
  );
};

interface StatusBadgeProps {
  status: string;
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  const getVariant = (status: string): 'default' | 'success' | 'warning' | 'error' | 'info' => {
    const statusVariants: Record<string, 'default' | 'success' | 'warning' | 'error' | 'info'> = {
      // Order statuses
      pending: 'warning',
      confirmed: 'info',
      assigned: 'info',
      in_progress: 'info',
      completed: 'success',
      cancelled: 'error',
      refunded: 'default',
      
      // Payment statuses
      paid: 'success',
      failed: 'error',
      
      // User statuses
      active: 'success',
      inactive: 'default',
      locked: 'error',
      
      // Provider verification statuses
      verified: 'success',
      rejected: 'error',
      
      // General statuses
      available: 'success',
      unavailable: 'error',
      blocked: 'error',
    };
    
    return statusVariants[status.toLowerCase()] || 'default';
  };

  const formatStatus = (status: string): string => {
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <Badge variant={getVariant(status)} className={className}>
      {formatStatus(status)}
    </Badge>
  );
};
